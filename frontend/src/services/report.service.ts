import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

interface SpectralAnalysisData {
  image: {
    id: number;
    name: string;
    capture_date: string;
    satellite_source: string;
    cloud_coverage: number;
    region: string;
  };
  indices: {
    ndvi: { mean: number; stddev: number; computed_at: string };
    ndwi: { mean: number; stddev: number; computed_at: string };
    ndti: { mean: number; stddev: number; computed_at: string };
  };
  riskAssessment: {
    overallRisk: 'LOW' | 'MODERATE' | 'HIGH' | 'CRITICAL';
    riskScore: number;
    financialImpact: {
      estimatedLoss: number;
      currency: string;
      timeframe: string;
    };
    recommendations: string[];
  };
}

interface FinancialRiskAnalysis {
  miningActivityDetected: boolean;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  areaHectares: number;

  // Coûts basés sur votre configuration financière (FCFA)
  deforestationCost: number;      // 9.3M FCFA/ha
  waterPollutionCost: number;     // 5M FCFA/ha
  soilDegradationCost: number;    // 3M FCFA/ha
  biodiversityLossCost: number;   // 2M FCFA/ha

  // Facteurs multiplicateurs
  intensityFactor: number;        // Basé sur indices spectraux
  distanceFactor: number;         // Proximité zones sensibles
  occurrenceFactor: number;       // Récurrence détections

  // Totaux
  totalEstimatedLoss: number;     // En FCFA
  totalEstimatedLossEUR: number;  // Conversion EUR
  preventionCost: number;
  costBenefitRatio: number;

  // Seuils de risque (FCFA)
  riskThresholds: {
    CRITICAL: number;  // 10M FCFA
    HIGH: number;      // 5M FCFA
    MEDIUM: number;    // 1M FCFA
    LOW: number;       // < 1M FCFA
  };
}

class ReportService {
  private readonly COLORS = {
    primary: '#F97316', // Orange
    secondary: '#F59E0B', // Amber
    success: '#10B981',
    warning: '#F59E0B',
    danger: '#EF4444',
    dark: '#1F2937',
    light: '#F9FAFB'
  };

  async generateSpectralAnalysisReport(data: SpectralAnalysisData): Promise<void> {
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // Configuration des marges
    const margin = 20;
    const contentWidth = pageWidth - 2 * margin;
    let currentY = margin;

    // En-tête avec logo et titre
    this.addHeader(pdf, currentY, contentWidth, margin);
    currentY += 40;

    // Informations de l'image
    currentY = this.addImageInfo(pdf, data.image, currentY, contentWidth, margin);
    currentY += 10;

    // Analyse des risques financiers
    const riskAnalysis = this.calculateFinancialRisk(data.indices);
    currentY = this.addFinancialRiskSection(pdf, riskAnalysis, currentY, contentWidth, margin);

    // Nouvelle page pour les détails techniques
    pdf.addPage();
    currentY = margin;

    // Détails des indices spectraux
    currentY = this.addSpectralIndicesSection(pdf, data.indices, currentY, contentWidth, margin);

    // Recommandations
    currentY = this.addRecommendationsSection(pdf, riskAnalysis, currentY, contentWidth, margin);

    // Pied de page
    this.addFooter(pdf, pageHeight);

    // Téléchargement du PDF
    const fileName = `Rapport_Analyse_Spectrale_${data.image.name}_${new Date().toISOString().split('T')[0]}.pdf`;
    pdf.save(fileName);
  }

  private addHeader(pdf: jsPDF, y: number, contentWidth: number, margin: number): void {
    // Titre principal
    pdf.setFontSize(24);
    pdf.setTextColor(249, 115, 22); // Orange #F97316
    pdf.setFont('helvetica', 'bold');
    pdf.text('GOLDSENTINEL', margin, y);

    // Sous-titre
    pdf.setFontSize(16);
    pdf.setTextColor(31, 41, 55); // Dark #1F2937
    pdf.setFont('helvetica', 'normal');
    pdf.text('Rapport d\'Analyse Spectrale & Évaluation des Risques Financiers', margin, y + 10);

    // Date de génération
    pdf.setFontSize(10);
    pdf.setTextColor(100, 100, 100);
    const currentDate = new Date().toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
    pdf.text(`Généré le ${currentDate}`, margin, y + 20);

    // Ligne de séparation
    pdf.setDrawColor(249, 115, 22); // Orange
    pdf.setLineWidth(2);
    pdf.line(margin, y + 25, margin + contentWidth, y + 25);
  }

  private addImageInfo(pdf: jsPDF, image: any, y: number, contentWidth: number, margin: number): number {
    pdf.setFontSize(14);
    pdf.setTextColor(31, 41, 55); // Dark
    pdf.setFont('helvetica', 'bold');
    pdf.text('INFORMATIONS DE L\'IMAGE SATELLITE', margin, y);

    y += 10;
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(31, 41, 55); // Dark

    const info = [
      [`Nom de l'image:`, image.name],
      [`Date de capture:`, new Date(image.capture_date).toLocaleDateString('fr-FR')],
      [`Satellite:`, image.satellite_source],
      [`Couverture nuageuse:`, `${image.cloud_coverage}%`],
      [`Région:`, image.region],
      [`ID de l'image:`, `#${image.id}`]
    ];

    info.forEach(([label, value]) => {
      pdf.setFont('helvetica', 'bold');
      pdf.text(label, margin, y);
      pdf.setFont('helvetica', 'normal');
      pdf.text(value, margin + 50, y);
      y += 6;
    });

    return y;
  }

  private calculateFinancialRisk(indices: any): FinancialRiskAnalysis {
    // Vérification de sécurité pour les valeurs nulles
    const ndvi = indices.ndvi?.mean ?? 0.5;
    const ndwi = indices.ndwi?.mean ?? 0;
    const ndti = indices.ndti?.mean ?? 0;

    // Configuration financière basée sur votre système (FCFA)
    const BASE_COSTS = {
      deforestation: 9_300_000,    // 9.3M FCFA/ha - Impact principal orpaillage
      water_pollution: 5_000_000,  // 5M FCFA/ha - Pollution hydrique
      soil_degradation: 3_000_000, // 3M FCFA/ha - Dégradation sols
      biodiversity_loss: 2_000_000 // 2M FCFA/ha - Perte biodiversité
    };

    // Seuils de risque (FCFA) - basés sur votre configuration
    const RISK_THRESHOLDS = {
      CRITICAL: 10_000_000,  // 10M FCFA
      HIGH: 5_000_000,       // 5M FCFA
      MEDIUM: 1_000_000,     // 1M FCFA
      LOW: 0                 // < 1M FCFA
    };

    // Détection d'activité minière basée sur vos seuils
    const miningActivityDetected = ndti > 0.6 && ndvi < 0.7; // Seuils de votre config

    // Estimation de la surface affectée (hectares)
    // Basé sur l'intensité des anomalies spectrales
    let areaHectares = 0;
    if (miningActivityDetected) {
      // Surface proportionnelle à l'intensité des anomalies
      const ndviAnomalyIntensity = Math.max(0, (0.7 - ndvi) / 0.7);
      const ndtiAnomalyIntensity = Math.max(0, ndti);
      const combinedIntensity = (ndviAnomalyIntensity + ndtiAnomalyIntensity) / 2;
      areaHectares = Math.min(combinedIntensity * 50, 50); // Max 50 hectares
    } else {
      areaHectares = Math.random() * 5; // Surface minimale pour test
    }

    // Calcul du facteur d'intensité basé sur indices spectraux
    let intensityFactor = 1.0;

    // NDVI - Déforestation sévère
    if (ndvi < 0.7) {
      intensityFactor += 0.5; // Facteur NDVI de votre config
    }

    // NDWI - Pollution eau
    if (ndwi > 0.5) {
      intensityFactor += 0.3; // Facteur NDWI de votre config
    }

    // NDTI - Perturbation sols
    if (ndti > 0.6) {
      intensityFactor += 0.2; // Facteur NDTI de votre config
    }

    // Facteurs multiplicateurs
    const distanceFactor = 1.5; // Supposé zone sensible (1-5km)
    const occurrenceFactor = 1.0; // Première occurrence

    // Calculs des coûts par type d'impact (FCFA)
    const deforestationCost = areaHectares * BASE_COSTS.deforestation * intensityFactor;
    const waterPollutionCost = areaHectares * BASE_COSTS.water_pollution * (ndwi > 0.1 ? 1 : 0.1);
    const soilDegradationCost = areaHectares * BASE_COSTS.soil_degradation * (ndti > 0.1 ? 1 : 0.1);
    const biodiversityLossCost = areaHectares * BASE_COSTS.biodiversity_loss * intensityFactor;

    // Coût total en FCFA
    const totalEstimatedLoss = (deforestationCost + waterPollutionCost + soilDegradationCost + biodiversityLossCost)
                              * distanceFactor * occurrenceFactor;

    // Pas de conversion - tout en FCFA (XOF)
    const totalEstimatedLossEUR = totalEstimatedLoss; // Garde en FCFA

    // Détermination du niveau de risque
    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    if (totalEstimatedLoss >= RISK_THRESHOLDS.CRITICAL) {
      riskLevel = 'CRITICAL';
    } else if (totalEstimatedLoss >= RISK_THRESHOLDS.HIGH) {
      riskLevel = 'HIGH';
    } else if (totalEstimatedLoss >= RISK_THRESHOLDS.MEDIUM) {
      riskLevel = 'MEDIUM';
    } else {
      riskLevel = 'LOW';
    }

    // Coût de prévention (20% du coût total)
    const preventionCost = totalEstimatedLoss * 0.2;
    const costBenefitRatio = totalEstimatedLoss / preventionCost;

    return {
      miningActivityDetected,
      riskLevel,
      areaHectares,
      deforestationCost,
      waterPollutionCost,
      soilDegradationCost,
      biodiversityLossCost,
      intensityFactor,
      distanceFactor,
      occurrenceFactor,
      totalEstimatedLoss,
      totalEstimatedLossEUR,
      preventionCost,
      costBenefitRatio,
      riskThresholds: RISK_THRESHOLDS
    };
  }

  private addFinancialRiskSection(pdf: jsPDF, risk: FinancialRiskAnalysis, y: number, contentWidth: number, margin: number): number {
    // Titre de section
    pdf.setFontSize(14);
    pdf.setTextColor(239, 68, 68); // Red
    pdf.setFont('helvetica', 'bold');
    pdf.text('ÉVALUATION DES RISQUES FINANCIERS', margin, y);

    y += 15;

    // Statut de détection avec niveau de risque
    const statusText = risk.miningActivityDetected ? 'ACTIVITÉ MINIÈRE DÉTECTÉE' : 'AUCUNE ACTIVITÉ SUSPECTE';

    pdf.setFontSize(12);
    if (risk.miningActivityDetected) {
      pdf.setTextColor(239, 68, 68); // Red
    } else {
      pdf.setTextColor(16, 185, 129); // Green
    }
    pdf.setFont('helvetica', 'bold');
    pdf.text(`🚨 ${statusText} - NIVEAU ${risk.riskLevel}`, margin, y);

    y += 10;

    // Informations de base
    pdf.setFontSize(10);
    pdf.setTextColor(31, 41, 55); // Dark
    pdf.setFont('helvetica', 'normal');
    pdf.text(`Surface affectée estimée: ${risk.areaHectares.toFixed(1)} hectares`, margin, y);
    y += 6;
    pdf.text(`Facteur d'intensité: ${risk.intensityFactor.toFixed(2)} (basé sur indices spectraux)`, margin, y);
    y += 6;
    pdf.text(`Facteur de distance: ${risk.distanceFactor.toFixed(1)} (proximité zones sensibles)`, margin, y);

    y += 15;

    // Tableau détaillé des coûts (FCFA uniquement)
    const costData = [
      ['Type d\'impact', 'Coût unitaire', 'Surface (ha)', 'Coût total (FCFA)'],
      ['Déforestation', '9.3M FCFA/ha', risk.areaHectares.toFixed(1), this.formatCurrencyFCFA(risk.deforestationCost)],
      ['Pollution hydrique', '5M FCFA/ha', risk.areaHectares.toFixed(1), this.formatCurrencyFCFA(risk.waterPollutionCost)],
      ['Dégradation sols', '3M FCFA/ha', risk.areaHectares.toFixed(1), this.formatCurrencyFCFA(risk.soilDegradationCost)],
      ['Perte biodiversité', '2M FCFA/ha', risk.areaHectares.toFixed(1), this.formatCurrencyFCFA(risk.biodiversityLossCost)],
      ['', 'TOTAL', '', this.formatCurrencyFCFA(risk.totalEstimatedLoss)]
    ];

    this.addTable(pdf, costData, margin, y, contentWidth);
    y += costData.length * 8 + 15;

    // Seuils de risque
    pdf.setFontSize(12);
    pdf.setTextColor(249, 115, 22); // Orange
    pdf.setFont('helvetica', 'bold');
    pdf.text('CLASSIFICATION DES RISQUES', margin, y);

    y += 10;
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(31, 41, 55); // Dark

    const riskClassification = [
      `🔴 CRITIQUE: ≥ ${this.formatCurrencyFCFA(risk.riskThresholds.CRITICAL)}`,
      `🟠 ÉLEVÉ: ≥ ${this.formatCurrencyFCFA(risk.riskThresholds.HIGH)}`,
      `🟡 MOYEN: ≥ ${this.formatCurrencyFCFA(risk.riskThresholds.MEDIUM)}`,
      `🟢 FAIBLE: < ${this.formatCurrencyFCFA(risk.riskThresholds.MEDIUM)}`
    ];

    riskClassification.forEach(text => {
      pdf.text(text, margin, y);
      y += 6;
    });

    y += 10;

    // Analyse coût-bénéfice
    pdf.setFontSize(12);
    pdf.setTextColor(249, 115, 22); // Orange
    pdf.setFont('helvetica', 'bold');
    pdf.text('ANALYSE COÛT-BÉNÉFICE DE LA PRÉVENTION', margin, y);

    y += 10;
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(31, 41, 55); // Dark

    const benefitText = [
      `Coût de prévention (20%): ${this.formatCurrencyFCFA(risk.preventionCost)}`,
      `Coût de réparation: ${this.formatCurrencyFCFA(risk.totalEstimatedLoss)}`,
      `Ratio coût-bénéfice: 1:${risk.costBenefitRatio.toFixed(1)}`,
      ``,
      `💡 Chaque franc CFA investi en prévention permet d'économiser ${risk.costBenefitRatio.toFixed(1)} FCFA en réparation.`,
      `📊 Basé sur les données du Ministère ivoirien: 3000 milliards FCFA/an de dommages liés à l'orpaillage.`
    ];

    benefitText.forEach(text => {
      pdf.text(text, margin, y);
      y += 6;
    });

    return y + 10;
  }

  private addSpectralIndicesSection(pdf: jsPDF, indices: any, y: number, contentWidth: number, margin: number): number {
    pdf.setFontSize(14);
    pdf.setTextColor(249, 115, 22); // Orange
    pdf.setFont('helvetica', 'bold');
    pdf.text('DÉTAILS DES INDICES SPECTRAUX', margin, y);

    y += 15;

    const indicesData = [
      ['Indice', 'Valeur', 'Écart-type', 'Interprétation'],
      ['NDVI (Végétation)',
       indices.ndvi?.mean?.toFixed(3) ?? 'N/A',
       indices.ndvi?.stddev?.toFixed(3) ?? 'N/A',
       this.interpretNDVI(indices.ndvi?.mean ?? 0.5)],
      ['NDWI (Eau)',
       indices.ndwi?.mean?.toFixed(3) ?? 'N/A',
       indices.ndwi?.stddev?.toFixed(3) ?? 'N/A',
       this.interpretNDWI(indices.ndwi?.mean ?? 0)],
      ['NDTI (Sol)',
       indices.ndti?.mean?.toFixed(3) ?? 'N/A',
       indices.ndti?.stddev?.toFixed(3) ?? 'N/A',
       this.interpretNDTI(indices.ndti?.mean ?? 0)]
    ];

    this.addTable(pdf, indicesData, margin, y, contentWidth);

    return y + indicesData.length * 8 + 20;
  }

  private addRecommendationsSection(pdf: jsPDF, risk: FinancialRiskAnalysis, y: number, contentWidth: number, margin: number): number {
    pdf.setFontSize(14);
    pdf.setTextColor(249, 115, 22); // Orange
    pdf.setFont('helvetica', 'bold');
    pdf.text('RECOMMANDATIONS', margin, y);

    y += 15;

    const recommendations = this.generateRecommendations(risk);

    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(31, 41, 55); // Dark

    recommendations.forEach((rec, index) => {
      pdf.text(`${index + 1}. ${rec}`, margin, y);
      y += 8;
    });

    return y;
  }

  private addTable(pdf: jsPDF, data: string[][], x: number, y: number, width: number): void {
    const rowHeight = 8;
    const colWidth = width / data[0].length;

    data.forEach((row, rowIndex) => {
      row.forEach((cell, colIndex) => {
        const cellX = x + colIndex * colWidth;
        const cellY = y + rowIndex * rowHeight;

        if (rowIndex === 0) {
          // En-tête
          pdf.setFont('helvetica', 'bold');
          pdf.setFillColor(249, 250, 251); // Light gray
          pdf.rect(cellX, cellY - 5, colWidth, rowHeight, 'F');
        } else {
          pdf.setFont('helvetica', 'normal');
        }

        pdf.text(cell, cellX + 2, cellY);
      });
    });
  }

  private addFooter(pdf: jsPDF, pageHeight: number): void {
    const y = pageHeight - 15;
    pdf.setFontSize(8);
    pdf.setTextColor(100, 100, 100);
    pdf.text('GoldSentinel - Surveillance de l\'orpaillage illégal par analyse spectrale', 20, y);
    pdf.text(`Page ${pdf.internal.getCurrentPageInfo().pageNumber}`, 180, y);
  }

  private interpretNDVI(value: number): string {
    if (value > 0.6) return 'Végétation très dense';
    if (value > 0.4) return 'Végétation dense';
    if (value > 0.2) return 'Végétation modérée';
    if (value > 0) return 'Végétation clairsemée';
    return 'Sol nu / Absence végétation';
  }

  private interpretNDWI(value: number): string {
    if (value > 0.3) return 'Forte présence d\'eau';
    if (value > 0.1) return 'Présence d\'eau modérée';
    if (value > -0.1) return 'Faible humidité';
    return 'Zone sèche';
  }

  private interpretNDTI(value: number): string {
    if (value > 0.2) return 'Sol fortement perturbé';
    if (value > 0.1) return 'Sol modifié';
    if (value > 0) return 'Légère modification';
    return 'Sol naturel';
  }

  private generateRecommendations(risk: FinancialRiskAnalysis): string[] {
    const recommendations = [];

    if (risk.miningActivityDetected) {
      recommendations.push('Mission de terrain urgente pour confirmer l\'activité minière détectée');
      recommendations.push('Notification immédiate aux autorités compétentes');
      recommendations.push('Mise en place d\'une surveillance renforcée de la zone');
    }

    if (risk.vegetationLoss.percentage > 10) {
      recommendations.push('Plan de reforestation à mettre en œuvre rapidement');
      recommendations.push('Évaluation de l\'impact sur la biodiversité locale');
    }

    if (risk.waterContamination.severity !== 'LOW') {
      recommendations.push('Analyse de la qualité de l\'eau dans les cours d\'eau environnants');
      recommendations.push('Mise en place de systèmes de traitement de l\'eau si nécessaire');
    }

    if (risk.soilDegradation.severity !== 'LOW') {
      recommendations.push('Étude pédologique approfondie de la zone affectée');
      recommendations.push('Plan de réhabilitation des sols dégradés');
    }

    recommendations.push('Renforcement des patrouilles préventives dans la région');
    recommendations.push('Sensibilisation des communautés locales aux risques environnementaux');

    return recommendations;
  }

  private formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  private formatCurrencyFCFA(amount: number): string {
    return new Intl.NumberFormat('fr-FR', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount) + ' FCFA';
  }

  private formatCurrencyXOF(amount: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }


}

export const reportService = new ReportService();
