import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

interface SpectralAnalysisData {
  image: {
    id: number;
    name: string;
    capture_date: string;
    satellite_source: string;
    cloud_coverage: number;
    region: string;
  };
  indices: {
    ndvi: { mean: number; stddev: number; computed_at: string };
    ndwi: { mean: number; stddev: number; computed_at: string };
    ndti: { mean: number; stddev: number; computed_at: string };
  };
  riskAssessment: {
    overallRisk: 'LOW' | 'MODERATE' | 'HIGH' | 'CRITICAL';
    riskScore: number;
    financialImpact: {
      estimatedLoss: number;
      currency: string;
      timeframe: string;
    };
    recommendations: string[];
  };
}

interface FinancialRiskAnalysis {
  miningActivityDetected: boolean;
  vegetationLoss: {
    percentage: number;
    areaKm2: number;
    restorationCost: number;
  };
  waterContamination: {
    severity: 'LOW' | 'MODERATE' | 'HIGH';
    treatmentCost: number;
  };
  soilDegradation: {
    severity: 'LOW' | 'MODERATE' | 'HIGH';
    rehabilitationCost: number;
  };
  totalEstimatedCost: number;
  preventionCost: number;
  costBenefitRatio: number;
}

class ReportService {
  private readonly COLORS = {
    primary: '#F97316', // Orange
    secondary: '#F59E0B', // Amber
    success: '#10B981',
    warning: '#F59E0B',
    danger: '#EF4444',
    dark: '#1F2937',
    light: '#F9FAFB'
  };

  async generateSpectralAnalysisReport(data: SpectralAnalysisData): Promise<void> {
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // Configuration des marges
    const margin = 20;
    const contentWidth = pageWidth - 2 * margin;
    let currentY = margin;

    // En-tête avec logo et titre
    this.addHeader(pdf, currentY, contentWidth, margin);
    currentY += 40;

    // Informations de l'image
    currentY = this.addImageInfo(pdf, data.image, currentY, contentWidth, margin);
    currentY += 10;

    // Analyse des risques financiers
    const riskAnalysis = this.calculateFinancialRisk(data.indices);
    currentY = this.addFinancialRiskSection(pdf, riskAnalysis, currentY, contentWidth, margin);

    // Nouvelle page pour les détails techniques
    pdf.addPage();
    currentY = margin;

    // Détails des indices spectraux
    currentY = this.addSpectralIndicesSection(pdf, data.indices, currentY, contentWidth, margin);

    // Recommandations
    currentY = this.addRecommendationsSection(pdf, riskAnalysis, currentY, contentWidth, margin);

    // Pied de page
    this.addFooter(pdf, pageHeight);

    // Téléchargement du PDF
    const fileName = `Rapport_Analyse_Spectrale_${data.image.name}_${new Date().toISOString().split('T')[0]}.pdf`;
    pdf.save(fileName);
  }

  private addHeader(pdf: jsPDF, y: number, contentWidth: number, margin: number): void {
    // Titre principal
    pdf.setFontSize(24);
    pdf.setTextColor(this.hexToRgb(this.COLORS.primary));
    pdf.setFont('helvetica', 'bold');
    pdf.text('GOLDSENTINEL', margin, y);

    // Sous-titre
    pdf.setFontSize(16);
    pdf.setTextColor(this.hexToRgb(this.COLORS.dark));
    pdf.setFont('helvetica', 'normal');
    pdf.text('Rapport d\'Analyse Spectrale & Évaluation des Risques Financiers', margin, y + 10);

    // Date de génération
    pdf.setFontSize(10);
    pdf.setTextColor(100, 100, 100);
    const currentDate = new Date().toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
    pdf.text(`Généré le ${currentDate}`, margin, y + 20);

    // Ligne de séparation
    pdf.setDrawColor(this.hexToRgb(this.COLORS.primary));
    pdf.setLineWidth(2);
    pdf.line(margin, y + 25, margin + contentWidth, y + 25);
  }

  private addImageInfo(pdf: jsPDF, image: any, y: number, contentWidth: number, margin: number): number {
    pdf.setFontSize(14);
    pdf.setTextColor(this.hexToRgb(this.COLORS.dark));
    pdf.setFont('helvetica', 'bold');
    pdf.text('INFORMATIONS DE L\'IMAGE SATELLITE', margin, y);

    y += 10;
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');

    const info = [
      [`Nom de l'image:`, image.name],
      [`Date de capture:`, new Date(image.capture_date).toLocaleDateString('fr-FR')],
      [`Satellite:`, image.satellite_source],
      [`Couverture nuageuse:`, `${image.cloud_coverage}%`],
      [`Région:`, image.region],
      [`ID de l'image:`, `#${image.id}`]
    ];

    info.forEach(([label, value]) => {
      pdf.setFont('helvetica', 'bold');
      pdf.text(label, margin, y);
      pdf.setFont('helvetica', 'normal');
      pdf.text(value, margin + 50, y);
      y += 6;
    });

    return y;
  }

  private calculateFinancialRisk(indices: any): FinancialRiskAnalysis {
    // Vérification de sécurité pour les valeurs nulles
    const ndvi = indices.ndvi?.mean ?? 0.5;
    const ndwi = indices.ndwi?.mean ?? 0;
    const ndti = indices.ndti?.mean ?? 0;

    // Détection d'activité minière basée sur les seuils
    const miningActivityDetected = ndti > 0.15 && ndvi < 0.2;

    // Calcul de la perte de végétation
    const vegetationLossPercentage = Math.max(0, (0.6 - ndvi) / 0.6 * 100);
    const affectedAreaKm2 = vegetationLossPercentage * 120; // 12,000 km² * 1%
    const restorationCostPerKm2 = 50000; // 50,000 EUR par km²

    // Évaluation de la contamination de l'eau
    const waterSeverity = ndwi > 0.2 ? 'HIGH' : ndwi > 0.1 ? 'MODERATE' : 'LOW';
    const waterTreatmentCost = waterSeverity === 'HIGH' ? 500000 :
                              waterSeverity === 'MODERATE' ? 200000 : 50000;

    // Évaluation de la dégradation du sol
    const soilSeverity = ndti > 0.15 ? 'HIGH' : ndti > 0.1 ? 'MODERATE' : 'LOW';
    const soilRehabilitationCost = soilSeverity === 'HIGH' ? 800000 :
                                  soilSeverity === 'MODERATE' ? 300000 : 100000;

    const totalCost = (affectedAreaKm2 * restorationCostPerKm2) + waterTreatmentCost + soilRehabilitationCost;
    const preventionCost = totalCost * 0.2; // 20% du coût de réparation

    return {
      miningActivityDetected,
      vegetationLoss: {
        percentage: vegetationLossPercentage,
        areaKm2: affectedAreaKm2,
        restorationCost: affectedAreaKm2 * restorationCostPerKm2
      },
      waterContamination: {
        severity: waterSeverity as 'LOW' | 'MODERATE' | 'HIGH',
        treatmentCost: waterTreatmentCost
      },
      soilDegradation: {
        severity: soilSeverity as 'LOW' | 'MODERATE' | 'HIGH',
        rehabilitationCost: soilRehabilitationCost
      },
      totalEstimatedCost: totalCost,
      preventionCost,
      costBenefitRatio: totalCost / preventionCost
    };
  }

  private addFinancialRiskSection(pdf: jsPDF, risk: FinancialRiskAnalysis, y: number, contentWidth: number, margin: number): number {
    // Titre de section
    pdf.setFontSize(14);
    pdf.setTextColor(this.hexToRgb(this.COLORS.danger));
    pdf.setFont('helvetica', 'bold');
    pdf.text('ÉVALUATION DES RISQUES FINANCIERS', margin, y);

    y += 15;

    // Statut de détection
    const statusColor = risk.miningActivityDetected ? this.COLORS.danger : this.COLORS.success;
    const statusText = risk.miningActivityDetected ? 'ACTIVITÉ MINIÈRE DÉTECTÉE' : 'AUCUNE ACTIVITÉ SUSPECTE';

    pdf.setFontSize(12);
    pdf.setTextColor(this.hexToRgb(statusColor));
    pdf.setFont('helvetica', 'bold');
    pdf.text(`🚨 ${statusText}`, margin, y);

    y += 15;

    // Tableau des coûts
    pdf.setFontSize(10);
    pdf.setTextColor(this.hexToRgb(this.COLORS.dark));
    pdf.setFont('helvetica', 'normal');

    const costData = [
      ['Type de dommage', 'Sévérité', 'Coût estimé (EUR)'],
      ['Perte de végétation', `${risk.vegetationLoss.percentage.toFixed(1)}%`, this.formatCurrency(risk.vegetationLoss.restorationCost)],
      ['Contamination de l\'eau', risk.waterContamination.severity, this.formatCurrency(risk.waterContamination.treatmentCost)],
      ['Dégradation du sol', risk.soilDegradation.severity, this.formatCurrency(risk.soilDegradation.rehabilitationCost)],
      ['', 'TOTAL', this.formatCurrency(risk.totalEstimatedCost)]
    ];

    this.addTable(pdf, costData, margin, y, contentWidth);
    y += costData.length * 8 + 10;

    // Analyse coût-bénéfice
    pdf.setFontSize(12);
    pdf.setTextColor(this.hexToRgb(this.COLORS.primary));
    pdf.setFont('helvetica', 'bold');
    pdf.text('ANALYSE COÛT-BÉNÉFICE', margin, y);

    y += 10;
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(this.hexToRgb(this.COLORS.dark));

    const benefitText = [
      `Coût de prévention estimé: ${this.formatCurrency(risk.preventionCost)}`,
      `Coût de réparation estimé: ${this.formatCurrency(risk.totalEstimatedCost)}`,
      `Ratio coût-bénéfice: 1:${risk.costBenefitRatio.toFixed(1)}`,
      ``,
      `💡 Chaque euro investi en prévention permet d'économiser ${risk.costBenefitRatio.toFixed(1)} euros en réparation.`
    ];

    benefitText.forEach(text => {
      pdf.text(text, margin, y);
      y += 6;
    });

    return y + 10;
  }

  private addSpectralIndicesSection(pdf: jsPDF, indices: any, y: number, contentWidth: number, margin: number): number {
    pdf.setFontSize(14);
    pdf.setTextColor(this.hexToRgb(this.COLORS.primary));
    pdf.setFont('helvetica', 'bold');
    pdf.text('DÉTAILS DES INDICES SPECTRAUX', margin, y);

    y += 15;

    const indicesData = [
      ['Indice', 'Valeur', 'Écart-type', 'Interprétation'],
      ['NDVI (Végétation)',
       indices.ndvi?.mean?.toFixed(3) ?? 'N/A',
       indices.ndvi?.stddev?.toFixed(3) ?? 'N/A',
       this.interpretNDVI(indices.ndvi?.mean ?? 0.5)],
      ['NDWI (Eau)',
       indices.ndwi?.mean?.toFixed(3) ?? 'N/A',
       indices.ndwi?.stddev?.toFixed(3) ?? 'N/A',
       this.interpretNDWI(indices.ndwi?.mean ?? 0)],
      ['NDTI (Sol)',
       indices.ndti?.mean?.toFixed(3) ?? 'N/A',
       indices.ndti?.stddev?.toFixed(3) ?? 'N/A',
       this.interpretNDTI(indices.ndti?.mean ?? 0)]
    ];

    this.addTable(pdf, indicesData, margin, y, contentWidth);

    return y + indicesData.length * 8 + 20;
  }

  private addRecommendationsSection(pdf: jsPDF, risk: FinancialRiskAnalysis, y: number, contentWidth: number, margin: number): number {
    pdf.setFontSize(14);
    pdf.setTextColor(this.hexToRgb(this.COLORS.primary));
    pdf.setFont('helvetica', 'bold');
    pdf.text('RECOMMANDATIONS', margin, y);

    y += 15;

    const recommendations = this.generateRecommendations(risk);

    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(this.hexToRgb(this.COLORS.dark));

    recommendations.forEach((rec, index) => {
      pdf.text(`${index + 1}. ${rec}`, margin, y);
      y += 8;
    });

    return y;
  }

  private addTable(pdf: jsPDF, data: string[][], x: number, y: number, width: number): void {
    const rowHeight = 8;
    const colWidth = width / data[0].length;

    data.forEach((row, rowIndex) => {
      row.forEach((cell, colIndex) => {
        const cellX = x + colIndex * colWidth;
        const cellY = y + rowIndex * rowHeight;

        if (rowIndex === 0) {
          // En-tête
          pdf.setFont('helvetica', 'bold');
          pdf.setFillColor(this.hexToRgb(this.COLORS.light));
          pdf.rect(cellX, cellY - 5, colWidth, rowHeight, 'F');
        } else {
          pdf.setFont('helvetica', 'normal');
        }

        pdf.text(cell, cellX + 2, cellY);
      });
    });
  }

  private addFooter(pdf: jsPDF, pageHeight: number): void {
    const y = pageHeight - 15;
    pdf.setFontSize(8);
    pdf.setTextColor(100, 100, 100);
    pdf.text('GoldSentinel - Surveillance de l\'orpaillage illégal par analyse spectrale', 20, y);
    pdf.text(`Page ${pdf.internal.getCurrentPageInfo().pageNumber}`, 180, y);
  }

  private interpretNDVI(value: number): string {
    if (value > 0.6) return 'Végétation très dense';
    if (value > 0.4) return 'Végétation dense';
    if (value > 0.2) return 'Végétation modérée';
    if (value > 0) return 'Végétation clairsemée';
    return 'Sol nu / Absence végétation';
  }

  private interpretNDWI(value: number): string {
    if (value > 0.3) return 'Forte présence d\'eau';
    if (value > 0.1) return 'Présence d\'eau modérée';
    if (value > -0.1) return 'Faible humidité';
    return 'Zone sèche';
  }

  private interpretNDTI(value: number): string {
    if (value > 0.2) return 'Sol fortement perturbé';
    if (value > 0.1) return 'Sol modifié';
    if (value > 0) return 'Légère modification';
    return 'Sol naturel';
  }

  private generateRecommendations(risk: FinancialRiskAnalysis): string[] {
    const recommendations = [];

    if (risk.miningActivityDetected) {
      recommendations.push('Mission de terrain urgente pour confirmer l\'activité minière détectée');
      recommendations.push('Notification immédiate aux autorités compétentes');
      recommendations.push('Mise en place d\'une surveillance renforcée de la zone');
    }

    if (risk.vegetationLoss.percentage > 10) {
      recommendations.push('Plan de reforestation à mettre en œuvre rapidement');
      recommendations.push('Évaluation de l\'impact sur la biodiversité locale');
    }

    if (risk.waterContamination.severity !== 'LOW') {
      recommendations.push('Analyse de la qualité de l\'eau dans les cours d\'eau environnants');
      recommendations.push('Mise en place de systèmes de traitement de l\'eau si nécessaire');
    }

    if (risk.soilDegradation.severity !== 'LOW') {
      recommendations.push('Étude pédologique approfondie de la zone affectée');
      recommendations.push('Plan de réhabilitation des sols dégradés');
    }

    recommendations.push('Renforcement des patrouilles préventives dans la région');
    recommendations.push('Sensibilisation des communautés locales aux risques environnementaux');

    return recommendations;
  }

  private formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  private hexToRgb(hex: string): [number, number, number] {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? [
      parseInt(result[1], 16),
      parseInt(result[2], 16),
      parseInt(result[3], 16)
    ] : [0, 0, 0];
  }
}

export const reportService = new ReportService();
