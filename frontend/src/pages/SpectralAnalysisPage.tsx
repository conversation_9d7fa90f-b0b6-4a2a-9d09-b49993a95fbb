import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  MenuItem,
  Alert,
  Tabs,
  Tab,
  Chip,
  CircularProgress
} from '@mui/material';
import { imagesAPI } from '../services/api';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`spectral-tabpanel-${index}`}
      aria-labelledby={`spectral-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

export const SpectralAnalysisPage: React.FC = () => {
  const [selectedImageId, setSelectedImageId] = useState<number | null>(null);
  const [selectedRegionId, setSelectedRegionId] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState(0);

  // Récupération des images disponibles
  const { data: images, isLoading: imagesLoading } = useQuery({
    queryKey: ['images'],
    queryFn: () => imagesAPI.getAll(),
  });

  // Données spectrales simulées pour l'instant
  const spectralMaps = null;
  const spectralIndices = null;
  const spectralTrends = null;
  const mapsLoading = false;
  const indicesLoading = false;
  const trendsLoading = false;

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleImageSelect = (imageId: number) => {
    setSelectedImageId(imageId);
    const imagesList = Array.isArray(images) ? images : (images?.data || images?.results || []);
    if (Array.isArray(imagesList)) {
      const selectedImage = imagesList.find(img => img?.id === imageId);
      if (selectedImage) {
        setSelectedRegionId(selectedImage.region_id);
      }
    }
  };

  // Gestion sécurisée des données d'images
  const imagesList = Array.isArray(images) ? images : (images?.data || images?.results || []);
  const processedImages = Array.isArray(imagesList) ? imagesList.filter(img => img?.processing_status === 'COMPLETED') : [];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold', mb: 3 }}>
        Analyse Spectrale
      </Typography>

      {/* Sélection d'image */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Sélectionner une Image
          </Typography>
          <Grid container spacing={2} alignItems="center">
            <Grid sx={{ xs: 12, md: 6 }}>
              <TextField
                select
                fullWidth
                label="Image satellite"
                value={selectedImageId || ''}
                onChange={(e) => handleImageSelect(Number(e.target.value))}
                disabled={imagesLoading}
              >
                {processedImages.map((image) => (
                  <MenuItem key={image.id} value={image.id}>
                    {image.name} - {new Date(image.capture_date).toLocaleDateString()}
                    <Chip
                      label={image.satellite}
                      size="small"
                      sx={{ ml: 1 }}
                    />
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid sx={{ xs: 12, md: 6 }}>
              {selectedImageId && (
                <Alert severity="info">
                  Image sélectionnée : {processedImages.find(img => img.id === selectedImageId)?.name}
                </Alert>
              )}
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Contenu principal */}
      {selectedImageId && (
        <Box sx={{ width: '100%' }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={activeTab} onChange={handleTabChange}>
              <Tab label="Cartes Spectrales" />
              <Tab label="Graphiques & Tendances" />
              <Tab label="Données Détaillées" />
            </Tabs>
          </Box>

          <TabPanel value={activeTab} index={0}>
            <Card>
              <CardContent>
                <Alert severity="info">
                  Cartes spectrales en cours de développement.
                  Cette fonctionnalité sera disponible prochainement avec l'intégration Google Earth Engine.
                </Alert>
                <Box sx={{ mt: 2, p: 3, bgcolor: 'grey.100', borderRadius: 1 }}>
                  <Typography variant="h6" gutterBottom>
                    Aperçu des cartes spectrales
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                      <Box sx={{ p: 2, bgcolor: 'green.50', borderRadius: 1 }}>
                        <Typography variant="subtitle1" color="green.700">
                          NDVI - Indice de Végétation
                        </Typography>
                        <Typography variant="body2">
                          Analyse de la densité végétale
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Box sx={{ p: 2, bgcolor: 'blue.50', borderRadius: 1 }}>
                        <Typography variant="subtitle1" color="blue.700">
                          NDWI - Indice d'Eau
                        </Typography>
                        <Typography variant="body2">
                          Détection des zones humides
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Box sx={{ p: 2, bgcolor: 'orange.50', borderRadius: 1 }}>
                        <Typography variant="subtitle1" color="orange.700">
                          NDTI - Indice de Sol
                        </Typography>
                        <Typography variant="body2">
                          Analyse des perturbations du sol
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              </CardContent>
            </Card>
          </TabPanel>

          <TabPanel value={activeTab} index={1}>
            <Card>
              <CardContent>
                <Alert severity="info">
                  Graphiques et tendances spectrales en cours de développement.
                </Alert>
                <Box sx={{ mt: 2, p: 3, bgcolor: 'grey.100', borderRadius: 1 }}>
                  <Typography variant="h6" gutterBottom>
                    Données simulées
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body1">
                        <strong>NDVI moyen :</strong> 0.45 (Végétation modérée)
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body1">
                        <strong>NDWI moyen :</strong> 0.12 (Humidité faible)
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              </CardContent>
            </Card>
          </TabPanel>

          <TabPanel value={activeTab} index={2}>
            <Card>
              <CardContent>
                <Alert severity="info">
                  Données détaillées en cours de développement.
                </Alert>
                <Box sx={{ mt: 2 }}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={4}>
                      <Card variant="outlined">
                        <CardContent>
                          <Typography variant="h6" gutterBottom sx={{ color: '#4CAF50' }}>
                            NDVI - Végétation
                          </Typography>
                          <Typography variant="h4" gutterBottom>
                            0.45
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Moyenne: 0.450
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Écart-type: 0.125
                          </Typography>
                          <Typography variant="body2" sx={{ mt: 1 }}>
                            Végétation modérée
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <Card variant="outlined">
                        <CardContent>
                          <Typography variant="h6" gutterBottom sx={{ color: '#2196F3' }}>
                            NDWI - Eau
                          </Typography>
                          <Typography variant="h4" gutterBottom>
                            0.12
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Moyenne: 0.120
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Écart-type: 0.089
                          </Typography>
                          <Typography variant="body2" sx={{ mt: 1 }}>
                            Présence d'eau modérée
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <Card variant="outlined">
                        <CardContent>
                          <Typography variant="h6" gutterBottom sx={{ color: '#FF9800' }}>
                            NDTI - Sol
                          </Typography>
                          <Typography variant="h4" gutterBottom>
                            0.08
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Moyenne: 0.080
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Écart-type: 0.045
                          </Typography>
                          <Typography variant="body2" sx={{ mt: 1 }}>
                            Légère modification du sol
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>

                    <Grid item xs={12}>
                      <Card variant="outlined">
                        <CardContent>
                          <Typography variant="h6" gutterBottom>
                            Métadonnées de Traitement (Simulées)
                          </Typography>
                          <Typography variant="body2">
                            <strong>Statut:</strong> COMPLETED
                          </Typography>
                          <Typography variant="body2">
                            <strong>Traité le:</strong> {new Date().toLocaleString()}
                          </Typography>
                          <Typography variant="body2">
                            <strong>Source:</strong> Données de démonstration
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  </Grid>
                </Box>
              </CardContent>
            </Card>
          </TabPanel>
        </Box>
      )}

      {!selectedImageId && (
        <Alert severity="info" sx={{ mt: 3 }}>
          Sélectionnez une image pour commencer l'analyse spectrale
        </Alert>
      )}
    </Box>
  );
};
