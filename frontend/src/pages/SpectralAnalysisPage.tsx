import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { motion, AnimatePresence } from 'framer-motion';
import {
  BeakerIcon,
  MapIcon,
  ChartBarIcon,
  EyeIcon,
  CalendarIcon,
  CloudIcon,
  GlobeAltIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { imagesAPI } from '../services/api';
import { Card, Button, Loading } from '../components/ui';
import { Line<PERSON>hart, Bar<PERSON>hart } from '../components/charts/ChartComponents';
import toast from 'react-hot-toast';

import spectralService from '../services/spectral.service';
import type { SpectralMapsData, IndicesData, TrendsData } from '../services/spectral.service';
import { SpectralIndices, SpectralTrends } from '../components/spectral';
import { SpectralMapSimple } from '../components/spectral/SpectralMapSimple';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <AnimatePresence mode="wait">
      {value === index && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className="mt-6"
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export const SpectralAnalysisPage: React.FC = () => {
  const [selectedImageId, setSelectedImageId] = useState<number | null>(null);
  const [selectedRegionId, setSelectedRegionId] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState(0);

  // Récupération des images disponibles
  const { data: images, isLoading: imagesLoading } = useQuery({
    queryKey: ['images'],
    queryFn: () => imagesAPI.getAll(),
  });

  // Récupération des cartes spectrales
  const { data: spectralMaps, isLoading: mapsLoading, error: mapsError } = useQuery({
    queryKey: ['spectral-maps', selectedImageId],
    queryFn: () => spectralService.getSpectralMaps(selectedImageId!),
    enabled: !!selectedImageId,
  });

  // Récupération des données d'indices
  const { data: spectralIndices, isLoading: indicesLoading } = useQuery({
    queryKey: ['spectral-indices', selectedImageId],
    queryFn: () => spectralService.getIndicesData(selectedImageId!),
    enabled: !!selectedImageId,
  });

  // Récupération des tendances
  const { data: spectralTrends, isLoading: trendsLoading } = useQuery({
    queryKey: ['spectral-trends', selectedRegionId],
    queryFn: () => spectralService.getIndicesTrends(selectedRegionId!),
    enabled: !!selectedRegionId,
  });

  const handleImageSelect = (imageId: number) => {
    setSelectedImageId(imageId);
    const imagesList = Array.isArray(images) ? images : (images?.data || images?.results || []);
    if (Array.isArray(imagesList)) {
      const selectedImage = imagesList.find(img => img?.id === imageId);
      if (selectedImage) {
        setSelectedRegionId(selectedImage.region_id);
        toast.success(`Image sélectionnée : ${selectedImage.name}`);
      }
    }
  };

  const handleProcessImage = async (imageId: number) => {
    try {
      toast.loading('Traitement de l\'image en cours...');
      // Ici vous pourriez ajouter un appel API pour traiter l'image
      // await spectralService.processImage(imageId);
      toast.success('Image traitée avec succès');
    } catch (error) {
      toast.error('Erreur lors du traitement de l\'image');
    }
  };

  // Gestion sécurisée des données d'images
  const imagesList = Array.isArray(images) ? images : (images?.data || images?.results || []);
  const processedImages = Array.isArray(imagesList) ? imagesList.filter(img => img?.processing_status === 'COMPLETED') : [];
  const selectedImage = processedImages.find(img => img.id === selectedImageId);

  return (
    <div className="space-y-8">
      {/* En-tête moderne */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-orange-500 to-amber-500 rounded-2xl p-8 text-white shadow-2xl"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2 flex items-center">
              <BeakerIcon className="w-8 h-8 mr-3" />
              Analyse Spectrale
            </h1>
            <p className="text-orange-100 text-lg">
              Surveillance par indices spectraux avec Google Earth Engine
            </p>
            <div className="flex items-center mt-2 space-x-4 text-sm text-orange-200">
              <span className="flex items-center">
                <GlobeAltIcon className="w-4 h-4 mr-1" />
                Sentinel-2
              </span>
              <span className="flex items-center">
                <MapIcon className="w-4 h-4 mr-1" />
                Bondoukou
              </span>
            </div>
          </div>
          <div className="hidden md:block">
            <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
              <BeakerIcon className="w-12 h-12 text-white" />
            </div>
          </div>
        </div>
      </motion.div>

      {/* Sélection d'image moderne */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-slate-900 flex items-center">
              <EyeIcon className="w-5 h-5 mr-2 text-orange-500" />
              Sélection d'Image Satellite
            </h2>
            {selectedImageId && (
              <Button
                variant="secondary"
                size="sm"
                onClick={() => handleProcessImage(selectedImageId)}
                icon={ArrowPathIcon}
              >
                Retraiter
              </Button>
            )}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Image satellite
              </label>
              <select
                className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all"
                value={selectedImageId || ''}
                onChange={(e) => handleImageSelect(Number(e.target.value))}
                disabled={imagesLoading}
              >
                <option value="">Sélectionner une image...</option>
                {processedImages.map((image) => (
                  <option key={image.id} value={image.id}>
                    {image.name} - {new Date(image.capture_date).toLocaleDateString('fr-FR')}
                  </option>
                ))}
              </select>
            </div>

            {selectedImage && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-slate-50 rounded-lg p-4"
              >
                <h3 className="font-medium text-slate-900 mb-3">Informations de l'image</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-slate-600">Date de capture :</span>
                    <span className="font-medium flex items-center">
                      <CalendarIcon className="w-4 h-4 mr-1" />
                      {new Date(selectedImage.capture_date).toLocaleDateString('fr-FR')}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-slate-600">Couverture nuageuse :</span>
                    <span className="font-medium flex items-center">
                      <CloudIcon className="w-4 h-4 mr-1" />
                      {selectedImage.cloud_coverage}%
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-slate-600">Statut :</span>
                    <span className="flex items-center">
                      {selectedImage.processing_status === 'COMPLETED' ? (
                        <CheckCircleIcon className="w-4 h-4 text-green-500 mr-1" />
                      ) : selectedImage.processing_status === 'PROCESSING' ? (
                        <ClockIcon className="w-4 h-4 text-yellow-500 mr-1" />
                      ) : (
                        <ExclamationTriangleIcon className="w-4 h-4 text-red-500 mr-1" />
                      )}
                      <span className="text-sm font-medium">
                        {selectedImage.processing_status}
                      </span>
                    </span>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </Card>
      </motion.div>

      {/* Navigation par onglets moderne */}
      {selectedImageId && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <div className="flex space-x-1 bg-slate-100 p-1 rounded-lg">
            {[
              { id: 0, label: 'Cartes Spectrales', icon: MapIcon },
              { id: 1, label: 'Indices & Données', icon: ChartBarIcon },
              { id: 2, label: 'Tendances Temporelles', icon: ChartBarIcon },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all
                  ${activeTab === tab.id
                    ? 'bg-white text-orange-600 shadow-sm'
                    : 'text-slate-600 hover:text-slate-900 hover:bg-white/50'
                  }
                `}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>

          {/* Onglet 1: Cartes Spectrales */}
          <TabPanel value={activeTab} index={0}>
            {mapsLoading ? (
              <div className="flex justify-center py-12">
                <Loading size="lg" text="Génération des cartes spectrales..." />
              </div>
            ) : mapsError ? (
              <Card>
                <div className="text-center py-12">
                  <ExclamationTriangleIcon className="w-16 h-16 text-red-500 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-slate-900 mb-2">Erreur de chargement</h3>
                  <p className="text-slate-500 mb-4">Impossible de charger les cartes spectrales</p>
                  <Button
                    variant="primary"
                    onClick={() => window.location.reload()}
                    icon={ArrowPathIcon}
                  >
                    Réessayer
                  </Button>
                </div>
              </Card>
            ) : spectralMaps ? (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <SpectralMapSimple
                  title="NDVI - Végétation"
                  mapUrl={spectralMaps.spectral_maps.ndvi_map_url}
                  bounds={spectralMaps.spectral_maps.bounds}
                  colorScheme="vegetation"
                />
                <SpectralMapSimple
                  title="NDWI - Eau"
                  mapUrl={spectralMaps.spectral_maps.ndwi_map_url}
                  bounds={spectralMaps.spectral_maps.bounds}
                  colorScheme="water"
                />
                <SpectralMapSimple
                  title="NDTI - Sol"
                  mapUrl={spectralMaps.spectral_maps.ndti_map_url}
                  bounds={spectralMaps.spectral_maps.bounds}
                  colorScheme="soil"
                />
              </div>
            ) : (
              <Card>
                <div className="text-center py-12">
                  <MapIcon className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-slate-900 mb-2">Cartes en cours de génération</h3>
                  <p className="text-slate-500">Les cartes spectrales seront disponibles une fois le traitement terminé</p>
                </div>
              </Card>
            )}
          </TabPanel>

          {/* Onglet 2: Indices & Données */}
          <TabPanel value={activeTab} index={1}>
            {indicesLoading ? (
              <div className="flex justify-center py-12">
                <Loading size="lg" text="Calcul des indices spectraux..." />
              </div>
            ) : spectralIndices ? (
              <div className="space-y-8">
                <SpectralIndices
                  ndvi={spectralIndices.ndvi_data}
                  ndwi={spectralIndices.ndwi_data}
                  ndti={spectralIndices.ndti_data}
                />

                {/* Analyse d'anomalie */}
                <Card>
                  <h3 className="text-lg font-semibold text-slate-900 mb-4">Analyse d'Anomalie</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg border border-green-200">
                      <div className="text-2xl font-bold text-green-600 mb-1">
                        {spectralIndices.ndvi_mean.toFixed(3)}
                      </div>
                      <div className="text-sm text-green-700 font-medium">NDVI Moyen</div>
                      <div className="text-xs text-green-600 mt-1">
                        {spectralIndices.ndvi_mean > 0.4 ? 'Végétation saine' : 'Végétation dégradée'}
                      </div>
                    </div>

                    <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-sky-50 rounded-lg border border-blue-200">
                      <div className="text-2xl font-bold text-blue-600 mb-1">
                        {spectralIndices.ndwi_mean.toFixed(3)}
                      </div>
                      <div className="text-sm text-blue-700 font-medium">NDWI Moyen</div>
                      <div className="text-xs text-blue-600 mt-1">
                        {spectralIndices.ndwi_mean > 0.1 ? 'Présence d\'eau' : 'Zone sèche'}
                      </div>
                    </div>

                    <div className="text-center p-4 bg-gradient-to-br from-orange-50 to-amber-50 rounded-lg border border-orange-200">
                      <div className="text-2xl font-bold text-orange-600 mb-1">
                        {spectralIndices.ndti_mean.toFixed(3)}
                      </div>
                      <div className="text-sm text-orange-700 font-medium">NDTI Moyen</div>
                      <div className="text-xs text-orange-600 mt-1">
                        {spectralIndices.ndti_mean > 0.1 ? 'Sol perturbé' : 'Sol naturel'}
                      </div>
                    </div>
                  </div>

                  {spectralIndices.ndti_mean > 0.15 && (
                    <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center">
                        <ExclamationTriangleIcon className="w-5 h-5 text-red-500 mr-2" />
                        <div>
                          <div className="font-medium text-red-800">Alerte : Perturbation du sol détectée</div>
                          <div className="text-sm text-red-600">
                            L'indice NDTI élevé ({spectralIndices.ndti_mean.toFixed(3)}) indique une possible activité minière.
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </Card>
              </div>
            ) : (
              <Card>
                <div className="text-center py-12">
                  <ChartBarIcon className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-slate-900 mb-2">Données en cours de calcul</h3>
                  <p className="text-slate-500">Les indices spectraux seront disponibles une fois le traitement terminé</p>
                </div>
              </Card>
            )}
          </TabPanel>

          {/* Onglet 3: Tendances Temporelles */}
          <TabPanel value={activeTab} index={2}>
            {trendsLoading ? (
              <div className="flex justify-center py-12">
                <Loading size="lg" text="Analyse des tendances temporelles..." />
              </div>
            ) : spectralTrends ? (
              <SpectralTrends
                trends={spectralTrends.trends}
                period={spectralTrends.period}
              />
            ) : (
              <Card>
                <div className="text-center py-12">
                  <ChartBarIcon className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-slate-900 mb-2">Analyse temporelle</h3>
                  <p className="text-slate-500 mb-4">
                    Pas assez d'images analysées pour afficher les tendances temporelles
                  </p>
                  <p className="text-sm text-slate-400">
                    Au moins 3 images de la même région sont nécessaires
                  </p>
                </div>
              </Card>
            )}
          </TabPanel>
        </motion.div>
      )}

      {/* Message d'aide si aucune image sélectionnée */}
      {!selectedImageId && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <div className="text-center py-16">
              <BeakerIcon className="w-20 h-20 text-orange-300 mx-auto mb-6" />
              <h3 className="text-xl font-medium text-slate-900 mb-3">
                Analyse Spectrale avec Google Earth Engine
              </h3>
              <p className="text-slate-500 mb-6 max-w-md mx-auto">
                Sélectionnez une image satellite pour commencer l'analyse spectrale et détecter
                les activités d'orpaillage illégal grâce aux indices NDVI, NDWI et NDTI.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
                <div className="p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl mb-2">🌱</div>
                  <div className="font-medium text-green-800">NDVI</div>
                  <div className="text-sm text-green-600">Analyse de la végétation</div>
                </div>
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl mb-2">💧</div>
                  <div className="font-medium text-blue-800">NDWI</div>
                  <div className="text-sm text-blue-600">Détection de l'eau</div>
                </div>
                <div className="p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl mb-2">🏔️</div>
                  <div className="font-medium text-orange-800">NDTI</div>
                  <div className="text-sm text-orange-600">Perturbation du sol</div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      )}
    </div>
  );
};
