import React from 'react';
import { motion } from 'framer-motion';
import { User } from 'lucide-react';

interface AvatarProps {
  src?: string;
  alt?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  fallback?: string;
  className?: string;
  online?: boolean;
}

const sizeClasses = {
  sm: 'w-8 h-8 text-xs',
  md: 'w-10 h-10 text-sm',
  lg: 'w-12 h-12 text-base',
  xl: 'w-16 h-16 text-lg',
};

export const Avatar: React.FC<AvatarProps> = ({
  src,
  alt,
  size = 'md',
  fallback,
  className = '',
  online = false,
}) => {
  const [imageError, setImageError] = React.useState(false);

  const getInitials = (name?: string) => {
    if (!name) return '';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <motion.div
      className={`relative inline-block ${className}`}
      whileHover={{ scale: 1.05 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
    >
      <div
        className={`
          ${sizeClasses[size]}
          rounded-full
          overflow-hidden
          bg-gradient-to-br from-night-blue to-sky-blue
          flex items-center justify-center
          text-white font-medium
          shadow-lg
          ring-2 ring-white
        `}
      >
        {src && !imageError ? (
          <img
            src={src}
            alt={alt}
            className="w-full h-full object-cover"
            onError={() => setImageError(true)}
          />
        ) : fallback ? (
          <span className="font-semibold">
            {getInitials(fallback)}
          </span>
        ) : (
          <User className="w-1/2 h-1/2" />
        )}
      </div>
      
      {online && (
        <motion.div
          className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-emerald rounded-full ring-2 ring-white"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2 }}
        />
      )}
    </motion.div>
  );
};
