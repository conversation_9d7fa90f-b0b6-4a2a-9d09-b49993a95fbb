import React, { Fragment, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Bars3Icon,
  XMarkIcon,
  BellIcon,
} from '@heroicons/react/24/outline';
import {
  Home,
  Camera,
  AlertTriangle,
  Search,
  FileText,
  BarChart3,
  Beaker,
  Settings,
  User,
  LogOut,
  Shield,
  MapPin,
} from 'lucide-react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Avatar, DropdownMenu, ConfirmModal } from '../components/ui';
import toast from 'react-hot-toast';

const navigation = [
  { name: 'Tableau de bord', href: '/', icon: Home, color: 'text-blue-500' },
  { name: 'Images', href: '/images', icon: Camera, color: 'text-purple-500' },
  { name: 'Détections', href: '/detections', icon: Search, color: 'text-emerald-500' },
  { name: 'Analy<PERSON>pectrale', href: '/spectral', icon: Beaker, color: 'text-orange-500' },
  { name: '<PERSON><PERSON><PERSON>', href: '/alerts', icon: AlertTriangle, color: 'text-red-500' },
  { name: 'Investigations', href: '/investigations', icon: Shield, color: 'text-indigo-500' },
  { name: 'Rapports', href: '/reports', icon: FileText, color: 'text-teal-500' },
  { name: 'Statistiques', href: '/stats', icon: BarChart3, color: 'text-pink-500' },
  { name: 'Régions', href: '/regions', icon: MapPin, color: 'text-cyan-500' },
];

export const MainLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const { user, logout } = useAuth();
  const location = useLocation();

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      await logout();
      toast.success('Déconnexion réussie');
    } catch (error) {
      toast.error('Erreur lors de la déconnexion');
    } finally {
      setIsLoggingOut(false);
      setShowLogoutModal(false);
    }
  };

  const userMenuItems = [
    {
      label: 'Mon profil',
      icon: User,
      onClick: () => {
        // Navigate to profile page
        window.location.href = '/account';
      },
    },
    {
      label: 'Paramètres',
      icon: Settings,
      onClick: () => {
        // Navigate to settings
        toast.info('Paramètres - Bientôt disponible');
      },
    },
    {
      label: 'Déconnexion',
      icon: LogOut,
      onClick: () => setShowLogoutModal(true),
      danger: true,
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Mobile sidebar */}
      <Transition.Root show={sidebarOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={setSidebarOpen}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-gradient-to-b from-night-blue to-slate-900 px-6 pb-4 shadow-2xl">
                  {/* Logo */}
                  <div className="flex h-16 shrink-0 items-center">
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="flex items-center space-x-3"
                    >
                      <div className="w-8 h-8 bg-gradient-gold rounded-lg flex items-center justify-center">
                        <Shield className="w-5 h-5 text-night-blue" />
                      </div>
                      <span className="text-xl font-bold text-white">GoldSentinel</span>
                    </motion.div>
                  </div>

                  {/* Navigation */}
                  <nav className="flex flex-1 flex-col">
                    <ul role="list" className="flex flex-1 flex-col gap-y-2">
                      {navigation.map((item, index) => (
                        <motion.li
                          key={item.name}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                        >
                          <Link
                            to={item.href}
                            onClick={() => setSidebarOpen(false)}
                            className={`
                              group flex items-center gap-x-3 rounded-xl p-3 text-sm font-medium
                              transition-all duration-200 relative overflow-hidden
                              ${location.pathname === item.href
                                ? 'bg-gradient-gold text-night-blue shadow-glow-gold'
                                : 'text-slate-300 hover:text-white hover:bg-white/10'
                              }
                            `}
                          >
                            <item.icon className={`h-5 w-5 shrink-0 ${location.pathname === item.href ? 'text-night-blue' : item.color}`} />
                            {item.name}
                            {location.pathname === item.href && (
                              <motion.div
                                layoutId="activeTab"
                                className="absolute inset-0 bg-gradient-gold rounded-xl -z-10"
                                transition={{ type: "spring", stiffness: 300, damping: 30 }}
                              />
                            )}
                          </Link>
                        </motion.li>
                      ))}
                    </ul>
                  </nav>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Static sidebar for desktop */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-gradient-to-b from-night-blue to-slate-900 px-6 pb-4 shadow-2xl">
          {/* Logo */}
          <div className="flex h-16 shrink-0 items-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              className="flex items-center space-x-3"
            >
              <div className="w-10 h-10 bg-gradient-gold rounded-xl flex items-center justify-center shadow-glow-gold">
                <Shield className="w-6 h-6 text-night-blue" />
              </div>
              <div>
                <span className="text-xl font-bold text-white">GoldSentinel</span>
                <p className="text-xs text-slate-400">Surveillance Minière</p>
              </div>
            </motion.div>
          </div>

          {/* Navigation */}
          <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-2">
              {navigation.map((item, index) => (
                <motion.li
                  key={item.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Link
                    to={item.href}
                    className={`
                      group flex items-center gap-x-3 rounded-xl p-3 text-sm font-medium
                      transition-all duration-200 relative overflow-hidden
                      ${location.pathname === item.href
                        ? 'bg-gradient-gold text-night-blue shadow-glow-gold'
                        : 'text-slate-300 hover:text-white hover:bg-white/10'
                      }
                    `}
                  >
                    <item.icon className={`h-5 w-5 shrink-0 ${location.pathname === item.href ? 'text-night-blue' : item.color}`} />
                    {item.name}
                    {location.pathname === item.href && (
                      <motion.div
                        layoutId="activeTabDesktop"
                        className="absolute inset-0 bg-gradient-gold rounded-xl -z-10"
                        transition={{ type: "spring", stiffness: 300, damping: 30 }}
                      />
                    )}
                  </Link>
                </motion.li>
              ))}
            </ul>
          </nav>
        </div>
      </div>

      <div className="lg:pl-72">
        {/* Modern Header */}
        <motion.div
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 bg-white/80 backdrop-blur-lg border-b border-slate-200/50 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8"
        >
          {/* Mobile menu button */}
          <motion.button
            type="button"
            className="p-2.5 text-slate-700 lg:hidden rounded-lg hover:bg-slate-100 transition-colors"
            onClick={() => setSidebarOpen(true)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <span className="sr-only">Ouvrir le menu</span>
            <Bars3Icon className="h-6 w-6" />
          </motion.button>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            {/* Search bar - placeholder for future */}
            <div className="flex flex-1 items-center">
              <div className="hidden sm:block text-sm text-slate-600">
                Bienvenue, <span className="font-medium text-slate-900">{user?.full_name}</span>
              </div>
            </div>

            <div className="flex items-center gap-x-4 lg:gap-x-6">
              {/* Notifications */}
              <motion.button
                type="button"
                className="relative p-2 text-slate-600 hover:text-slate-900 rounded-lg hover:bg-slate-100 transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span className="sr-only">Voir les notifications</span>
                <BellIcon className="h-6 w-6" />
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
              </motion.button>

              {/* Profile dropdown */}
              <DropdownMenu
                trigger={
                  <div className="flex items-center gap-x-3 cursor-pointer p-2 rounded-lg hover:bg-slate-100 transition-colors">
                    <Avatar
                      fallback={user?.full_name}
                      size="sm"
                      online
                    />
                    <div className="hidden sm:block text-left">
                      <p className="text-sm font-medium text-slate-900">{user?.full_name}</p>
                      <p className="text-xs text-slate-500">{user?.job_title}</p>
                    </div>
                  </div>
                }
                items={userMenuItems}
                align="right"
              />
            </div>
          </div>
        </motion.div>

        {/* Main content */}
        <main className="py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="px-4 sm:px-6 lg:px-8"
          >
            {children}
          </motion.div>
        </main>
      </div>

      {/* Logout confirmation modal */}
      <ConfirmModal
        isOpen={showLogoutModal}
        onClose={() => setShowLogoutModal(false)}
        onConfirm={handleLogout}
        title="Confirmer la déconnexion"
        message="Êtes-vous sûr de vouloir vous déconnecter ? Vous devrez vous reconnecter pour accéder à l'application."
        confirmText="Se déconnecter"
        cancelText="Annuler"
        danger
        loading={isLoggingOut}
      />
    </div>
  );
};