@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Open+Sans:wght@400;500;600&family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Leaflet CSS */
@import 'leaflet/dist/leaflet.css';

@layer base {
  html {
    font-family: 'Open Sans', 'Roboto', sans-serif;
    font-size: 14px;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', 'Poppins', sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-blue text-white px-4 py-2 rounded-lg hover:shadow-glow transition-all duration-200;
  }

  .btn-secondary {
    @apply bg-gradient-gold text-night-blue px-4 py-2 rounded-lg hover:shadow-glow-gold transition-all duration-200;
  }

  .btn-danger {
    @apply bg-gradient-red text-white px-4 py-2 rounded-lg hover:shadow-glow-red transition-all duration-200;
  }

  .card {
    @apply bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-all duration-300;
  }

  .input-field {
    @apply w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-night-blue focus:border-transparent transition-all duration-200;
  }

  .glass-effect {
    @apply bg-white/80 backdrop-blur-lg border border-white/20;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-night-blue to-sky-blue bg-clip-text text-transparent;
  }

  .floating-card {
    @apply transform hover:-translate-y-2 transition-transform duration-300;
  }

  .pulse-ring {
    @apply absolute inset-0 rounded-full animate-ping;
  }

  .shimmer {
    @apply relative overflow-hidden;
  }

  .shimmer::before {
    @apply absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent;
    content: '';
    animation: shimmer 2s infinite;
  }
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

/* Scrollbar personnalisé */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-slate-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-slate-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-slate-400;
}
